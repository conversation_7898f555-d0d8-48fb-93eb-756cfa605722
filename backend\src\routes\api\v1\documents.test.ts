import express from 'express';
import request from 'supertest';

import { testData } from '../../../__tests__/simple-mocks.js';

import { createDocumentsRouter } from './documents.js';

describe('Documents Routes', () => {
  // Helper function to create a test app with specific mocks
  function createTestAppWithMocks() {
    const mockDocumentService = {
      createDocument: jest.fn(),
      getDocuments: jest.fn(),
      generateDownloadUrl: jest.fn(),
      deleteDocument: jest.fn(),
    };

    const mockLogger = {
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
    };

    const mockMiddlewareFactory = {
      createAuthWithPermission: jest.fn().mockReturnValue([
        (req, res, next) => {
          req.user = testData.user;
          next();
        },
      ]),
    };

    const documentsRouter = createDocumentsRouter({
      documentService: mockDocumentService as any,
      middlewareFactory: mockMiddlewareFactory as any,
      logger: mockLogger as any,
    });

    const app = express();
    app.use(express.json());
    app.use('/api/v1/documents', documentsRouter);

    return {
      app,
      mockDocumentService,
      mockLogger,
      mockMiddlewareFactory,
    };
  }

  describe('POST /upload-url', () => {
    it('should generate upload URL for valid request', async () => {
      const { app, mockDocumentService } = createTestAppWithMocks();

      // Use shared fixture to ensure consistency
      const mockServiceResponse = {
        ...testData.document,
        uploadUrl: 'https://s3.amazonaws.com/presigned-upload-url',
        s3Key: 'documents/test-key',
        expiresIn: 3600,
      };
      mockDocumentService.createDocument.mockResolvedValue(mockServiceResponse);

      const response = await request(app)
        .post('/api/v1/documents/upload-url')
        .send({
          fileName: 'test-document.pdf',
          originalName: 'Test Document.pdf',
          fileSize: 1024,
          mimeType: 'application/pdf',
        })
        .expect(201);

      // Validate API response structure matches shared types
      expect(response.body).toEqual({
        data: mockServiceResponse,
        meta: {
          tenantId: testData.user.tenantId,
          expiresIn: 3600,
        },
      });

      // Ensure response has required fields for frontend
      expect(response.body.data.uploadUrl).toBeDefined();
      expect(typeof response.body.data.uploadUrl).toBe('string');
      expect(response.body.data.expiresIn).toBeDefined();
      expect(typeof response.body.data.expiresIn).toBe('number');
    });
  });

  describe('GET /', () => {
    it('should list documents with pagination', async () => {
      const { app, mockDocumentService } = createTestAppWithMocks();

      const mockResponse = {
        documents: [testData.document],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
        },
      };
      mockDocumentService.getDocuments.mockResolvedValue(mockResponse);

      const response = await request(app)
        .get('/api/v1/documents?page=1&limit=10')
        .expect(200);

      expect(response.body).toEqual({
        data: [
          {
            ...testData.document,
            createdAt: testData.document.createdAt.toISOString(),
            updatedAt: testData.document.updatedAt.toISOString(),
          },
        ],
        meta: {
          pagination: mockResponse.pagination,
          tenantId: testData.user.tenantId,
        },
      });
    });
  });

  describe('GET /:id/url', () => {
    it('should generate download URL for valid document', async () => {
      const { app, mockDocumentService } = createTestAppWithMocks();

      const mockResponse = {
        downloadUrl: 'https://s3.amazonaws.com/presigned-download-url',
        expiresIn: 3600,
      };
      mockDocumentService.generateDownloadUrl.mockResolvedValue(mockResponse);

      const response = await request(app)
        .get('/api/v1/documents/test-doc-id/url')
        .expect(200);

      expect(response.body).toEqual({
        data: mockResponse,
        meta: {
          documentId: 'test-doc-id',
          tenantId: testData.user.tenantId,
        },
      });
      expect(mockDocumentService.generateDownloadUrl).toHaveBeenCalledWith(
        'test-doc-id',
        testData.user.tenantId,
        testData.user
      );
    });
  });

  describe('DELETE /:id', () => {
    it('should delete document successfully', async () => {
      const { app, mockDocumentService } = createTestAppWithMocks();

      const mockDeletedDocument = {
        ...testData.document,
        deletedAt: new Date('2024-01-01T12:00:00.000Z'),
      };
      mockDocumentService.deleteDocument.mockResolvedValue(mockDeletedDocument);

      const response = await request(app)
        .delete('/api/v1/documents/test-doc-id')
        .expect(200);

      expect(response.body).toEqual({
        data: {
          ...mockDeletedDocument,
          createdAt: mockDeletedDocument.createdAt.toISOString(),
          updatedAt: mockDeletedDocument.updatedAt.toISOString(),
          deletedAt: mockDeletedDocument.deletedAt.toISOString(),
        },
        meta: {
          tenantId: testData.user.tenantId,
          documentId: 'test-doc-id',
          deletedAt: mockDeletedDocument.deletedAt.toISOString(),
        },
      });
      expect(mockDocumentService.deleteDocument).toHaveBeenCalledWith(
        'test-doc-id',
        testData.user.tenantId,
        testData.user
      );
    });
  });
});
