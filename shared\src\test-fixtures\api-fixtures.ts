/**
 * Shared API Test Fixtures
 * 
 * Provides consistent test data that matches real API contracts.
 * Used across frontend, backend, and mobile tests to prevent mock/reality drift.
 */

import type {
  ApiResponse,
  CreateDocumentResponse,
  GetDocumentsResponse,
  Document,
  User,
  PaginationMeta,
} from '../types/api.types.js';

// Base test data
export const testUser: User = {
  id: 'test-user-id',
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  isActive: true,
  tenantId: 'test-tenant-id',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  userRoles: [{
    id: 'test-role-assignment-id',
    role: {
      id: 'test-role-id',
      name: 'Company Admin',
      type: 'COMPANY_ADMIN',
      description: 'Full access to company resources',
    },
  }],
  tenant: {
    id: 'test-tenant-id',
    name: 'Test Company',
    slug: 'test-company',
  },
};

export const testDocument: Document = {
  id: 'test-document-id',
  tenantId: 'test-tenant-id',
  fileName: 'test-document.pdf',
  originalName: 'Test Document.pdf',
  fileSize: 1024000,
  mimeType: 'application/pdf',
  s3Key: 'tenant-test-tenant-id/test-document-id-test-document.pdf',
  title: 'Test Document',
  description: 'A test document for unit testing',
  createdBy: 'test-user-id',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  creator: {
    id: 'test-user-id',
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
  },
};

export const testPagination: PaginationMeta = {
  page: 1,
  limit: 20,
  total: 1,
  totalPages: 1,
};

// API Response Fixtures (properly wrapped)
export const createDocumentResponseFixture: ApiResponse<CreateDocumentResponse> = {
  data: {
    ...testDocument,
    uploadUrl: 'https://test-bucket.s3.amazonaws.com/presigned-upload-url',
    expiresIn: 900,
  },
  meta: {
    tenantId: 'test-tenant-id',
    expiresIn: 900,
  },
};

export const getDocumentsResponseFixture: ApiResponse<GetDocumentsResponse> = {
  data: {
    documents: [testDocument],
    pagination: testPagination,
  },
  meta: {
    pagination: testPagination,
    tenantId: 'test-tenant-id',
  },
};

// Helper to create properly typed API responses
export function createApiResponse<T>(data: T, meta?: Record<string, unknown>): ApiResponse<T> {
  return {
    data,
    meta,
    timestamp: new Date().toISOString(),
  };
}

// Mock factory that ensures type safety
export function createTypedMock<T>(): jest.MockedFunction<(...args: any[]) => Promise<T>> {
  return jest.fn<Promise<T>, any[]>();
}
