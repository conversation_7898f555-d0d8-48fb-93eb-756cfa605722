/**
 * API Contract Testing Utilities
 * 
 * Validates that frontend API clients match backend API responses.
 * Prevents the type of contract mismatch that caused the presigned URL bug.
 */

import type { ApiResponse } from '../types/api.types.js';

/**
 * Validates that a response matches the expected ApiResponse structure
 */
export function validateApiResponse<T>(
  response: unknown,
  dataValidator: (data: unknown) => data is T
): response is ApiResponse<T> {
  if (!response || typeof response !== 'object') {
    return false;
  }

  const resp = response as Record<string, unknown>;

  // Must have data property
  if (!('data' in resp)) {
    throw new Error('API response missing required "data" property');
  }

  // Data must pass validation
  if (!dataValidator(resp.data)) {
    throw new Error('API response data does not match expected type');
  }

  // Meta is optional but must be object if present
  if ('meta' in resp && resp.meta !== undefined && typeof resp.meta !== 'object') {
    throw new Error('API response "meta" must be an object if present');
  }

  return true;
}

/**
 * Type guard for CreateDocumentResponse data
 */
export function isCreateDocumentResponse(data: unknown): data is import('../types/api.types.js').CreateDocumentResponse {
  if (!data || typeof data !== 'object') return false;
  
  const doc = data as Record<string, unknown>;
  
  return (
    typeof doc.id === 'string' &&
    typeof doc.uploadUrl === 'string' &&
    typeof doc.expiresIn === 'number' &&
    typeof doc.fileName === 'string' &&
    typeof doc.fileSize === 'number'
  );
}

/**
 * Integration test helper that validates API contract
 */
export async function testApiContract<T>(
  apiCall: () => Promise<unknown>,
  dataValidator: (data: unknown) => data is T,
  testName: string
): Promise<ApiResponse<T>> {
  try {
    const response = await apiCall();
    
    if (!validateApiResponse(response, dataValidator)) {
      throw new Error(`Contract validation failed for ${testName}`);
    }
    
    return response;
  } catch (error) {
    throw new Error(`API contract test failed for ${testName}: ${error}`);
  }
}

/**
 * Mock response builder that ensures proper ApiResponse structure
 */
export function buildMockApiResponse<T>(
  data: T,
  meta?: Record<string, unknown>
): ApiResponse<T> {
  return {
    data,
    meta,
    timestamp: new Date().toISOString(),
  };
}
