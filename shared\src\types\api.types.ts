/**
 * Shared API types for Tech Notes application
 * 
 * These types define the standard API request/response patterns used across
 * all platforms (frontend, backend, mobile) to ensure consistency.
 * 
 * IMPORTANT: This is the single source of truth for API contracts.
 * All platforms MUST use these types to prevent contract mismatches.
 */

// Base API response structure used by all endpoints
export interface ApiResponse<T> {
  data: T;
  meta?: Record<string, unknown>;
  timestamp?: string;
}

export interface ApiError {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

// Pagination metadata structure
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Document-related API types
export interface Document {
  id: string;
  tenantId: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  s3Key: string;
  title?: string;
  description?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreateDocumentRequest {
  fileName: string;
  originalName?: string;
  fileSize: number;
  mimeType: string;
  title?: string;
  description?: string;
}

export interface CreateDocumentResponse {
  id: string;
  tenantId: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  s3Key: string;
  title?: string;
  description?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  uploadUrl: string;
  expiresIn: number;
}

export interface GetDocumentsOptions {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'createdAt' | 'fileName' | 'fileSize';
  sortOrder?: 'asc' | 'desc';
}

export interface GetDocumentsResponse {
  documents: Document[];
  pagination: PaginationMeta;
}

export interface GetDownloadUrlResponse {
  downloadUrl: string;
  expiresIn: number;
}

// User-related API types
export interface User {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  userRoles?: Array<{
    id: string;
    role: {
      id: string;
      name: string;
      type: string;
      description: string;
    };
  }>;
  tenant?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface UsersResponse {
  data: User[];
  meta: {
    pagination: PaginationMeta;
    tenantId?: string;
  };
}

// Health check types
export interface HealthResponse {
  status: 'ok' | 'error';
  timestamp: string;
  version?: string;
  uptime?: number;
  database?: {
    status: 'connected' | 'disconnected';
    latency?: number;
  };
  redis?: {
    status: 'connected' | 'disconnected';
    latency?: number;
  };
}
