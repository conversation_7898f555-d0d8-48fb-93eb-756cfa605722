/**
 * Tech Notes Shared Workspace - Main Entry Point
 *
 * This file exports all shared utilities, types, and constants for use across
 * frontend, backend, and mobile platforms in the Tech Notes application.
 *
 * Import patterns:
 * - import { formatDate, capitalize } from '@tech-notes/shared';
 * - import { slugify } from '@tech-notes/shared';
 */

// Utility functions
export {
  formatDate,
  formatDateTime,
  capitalize,
  slugify,
} from "./utils/helpers.js";

// API constants
export {
  API_ENDPOINTS,
  ENGAGEMENT_ENDPOINTS,
  buildQueryParams,
} from "./constants/api.constants.js";

// Auth types and utilities
export type {
  RoleType,
  UserRoleWithContext,
  AuthContext,
  UserProfile,
  AuthStatus,
  OnboardingData,
  AuthenticatedRequest,
  AuthenticatedRequestBase,
  BackendAuthContext,
  ClerkAuthOptions,
} from "./types/auth.types.js";

export {
  ROLE_TYPES,
  hasRoleType,
  isSystemAdmin,
  isCompanyAdmin,
  hasPermission,
} from "./types/auth.types.js";

// API types
export type {
  ApiResponse,
  ApiError,
  PaginationMeta,
  Document,
  CreateDocumentRequest,
  CreateDocumentResponse,
  GetDocumentsOptions,
  GetDocumentsResponse,
  GetDownloadUrlResponse,
  User,
  UsersResponse,
  HealthResponse,
} from "./types/api.types.js";

// Version info for debugging
export const SHARED_VERSION = "1.0.0";
