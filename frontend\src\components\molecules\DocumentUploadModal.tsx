import React, { useState, useRef, useCallback } from "react";
import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ert } from "../index";
import { useTypedApi } from "../../services/api-client";
import { Upload, X, FileText, AlertCircle, CheckCircle } from "lucide-react";

export interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadSuccess?: () => void;
}

// File validation constants (matching backend)
const ALLOWED_MIME_TYPES: Record<string, { maxSize: number; extensions: string[] }> = {
  // PDF files
  'application/pdf': { maxSize: 50 * 1024 * 1024, extensions: ['.pdf'] }, // 50MB
  
  // Microsoft Office files
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': {
    maxSize: 25 * 1024 * 1024,
    extensions: ['.docx'],
  }, // 25MB
  'application/msword': { maxSize: 25 * 1024 * 1024, extensions: ['.doc'] }, // 25MB
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
    maxSize: 25 * 1024 * 1024,
    extensions: ['.xlsx'],
  }, // 25MB
  'application/vnd.ms-excel': {
    maxSize: 25 * 1024 * 1024,
    extensions: ['.xls'],
  }, // 25MB
  
  // Web and data files
  'text/html': { maxSize: 10 * 1024 * 1024, extensions: ['.html', '.htm'] }, // 10MB
  'text/csv': { maxSize: 10 * 1024 * 1024, extensions: ['.csv'] }, // 10MB
  
  // Image files
  'image/jpeg': { maxSize: 10 * 1024 * 1024, extensions: ['.jpg', '.jpeg'] }, // 10MB
  'image/png': { maxSize: 10 * 1024 * 1024, extensions: ['.png'] }, // 10MB
};

interface UploadState {
  file: File | null;
  progress: number;
  status: 'idle' | 'uploading' | 'success' | 'error';
  error?: string;
}

export const DocumentUploadModal: React.FC<DocumentUploadModalProps> = ({
  isOpen,
  onClose,
  onUploadSuccess,
}) => {
  const [uploadState, setUploadState] = useState<UploadState>({
    file: null,
    progress: 0,
    status: 'idle',
  });
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const api = useTypedApi();

  // Validate file type and size
  const validateFile = (file: File): string | null => {
    const mimeConfig = ALLOWED_MIME_TYPES[file.type];
    if (!mimeConfig) {
      return `File type ${file.type} is not supported. Allowed types: PDF, DOC/DOCX, XLS/XLSX, HTML, CSV, JPG/PNG`;
    }

    if (file.size > mimeConfig.maxSize) {
      const maxSizeMB = Math.round(mimeConfig.maxSize / (1024 * 1024));
      return `File size ${Math.round(file.size / (1024 * 1024))}MB exceeds maximum allowed size of ${maxSizeMB}MB`;
    }

    return null;
  };

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      // Step 1: Get presigned upload URL
      const response = await api.documents.createUploadUrl({
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
      });

      // Step 2: Upload file to S3 with progress tracking
      return new Promise<void>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadState(prev => ({ ...prev, progress }));
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve();
          } else {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed due to network error'));
        });

        xhr.open('PUT', response.uploadUrl);
        xhr.setRequestHeader('Content-Type', file.type);
        xhr.send(file);
      });
    },
    onMutate: () => {
      setUploadState(prev => ({ ...prev, status: 'uploading', progress: 0, error: undefined }));
    },
    onSuccess: () => {
      setUploadState(prev => ({ ...prev, status: 'success', progress: 100 }));
      toast.success('Document uploaded successfully!');
      onUploadSuccess?.();
      setTimeout(() => {
        handleClose();
      }, 1500);
    },
    onError: (error: Error) => {
      setUploadState(prev => ({ 
        ...prev, 
        status: 'error', 
        error: error.message || 'Upload failed' 
      }));
      toast.error('Failed to upload document');
    },
  });

  const handleFileSelect = useCallback((file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setUploadState({
        file: null,
        progress: 0,
        status: 'error',
        error: validationError,
      });
      return;
    }

    setUploadState({
      file,
      progress: 0,
      status: 'idle',
      error: undefined,
    });
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleUpload = () => {
    if (uploadState.file) {
      uploadMutation.mutate(uploadState.file);
    }
  };

  const handleClose = () => {
    setUploadState({
      file: null,
      progress: 0,
      status: 'idle',
    });
    setIsDragOver(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Upload Document"
      size="lg"
    >
      <div className="space-y-6">
        {/* Drag and Drop Zone */}
        <div
          className={`
            border-2 border-dashed rounded-lg p-8 text-center transition-colors
            ${isDragOver 
              ? 'border-primary-500 bg-primary-50' 
              : uploadState.status === 'error'
              ? 'border-red-300 bg-red-50'
              : 'border-gray-300 hover:border-gray-400'
            }
          `}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <div className="space-y-4">
            <div className="flex justify-center">
              {uploadState.status === 'success' ? (
                <CheckCircle className="h-12 w-12 text-green-500" />
              ) : uploadState.status === 'error' ? (
                <AlertCircle className="h-12 w-12 text-red-500" />
              ) : (
                <Upload className="h-12 w-12 text-gray-400" />
              )}
            </div>
            
            <div>
              <p className="text-lg font-medium text-gray-900">
                {uploadState.status === 'success' 
                  ? 'Upload Complete!' 
                  : 'Drop your document here'
                }
              </p>
              <p className="text-sm text-gray-500 mt-1">
                or{' '}
                <button
                  type="button"
                  className="text-primary-600 hover:text-primary-700 font-medium"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploadState.status === 'uploading'}
                >
                  browse files
                </button>
              </p>
            </div>

            <p className="text-xs text-gray-400">
              Supported: PDF (50MB), Office files (25MB), HTML/CSV/Images (10MB)
            </p>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept=".pdf,.doc,.docx,.xls,.xlsx,.html,.htm,.csv,.jpg,.jpeg,.png"
          onChange={handleFileInputChange}
          disabled={uploadState.status === 'uploading'}
        />

        {/* Selected File Display */}
        {uploadState.file && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-gray-400" />
                <div>
                  <p className="font-medium text-gray-900">{uploadState.file.name}</p>
                  <p className="text-sm text-gray-500">{formatFileSize(uploadState.file.size)}</p>
                </div>
              </div>
              {uploadState.status === 'idle' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setUploadState({ file: null, progress: 0, status: 'idle' })}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Progress Bar */}
            {uploadState.status === 'uploading' && (
              <div className="mt-4">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Uploading...</span>
                  <span>{uploadState.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadState.progress}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {uploadState.status === 'error' && uploadState.error && (
          <Alert variant="error">
            {uploadState.error}
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <Button
            variant="secondary"
            onClick={handleClose}
            disabled={uploadState.status === 'uploading'}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleUpload}
            disabled={!uploadState.file || uploadState.status === 'uploading' || uploadState.status === 'success'}
          >
            {uploadState.status === 'uploading' ? 'Uploading...' : 'Upload Document'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DocumentUploadModal;
