import React from "react";
import { Link, Outlet, useLocation } from "react-router-dom";
import { UserButton } from "@clerk/clerk-react";
import {
  SystemAdminOnly,
  CompanyAdminOnly,
  UserManagementAccess,
  TenantManagementAccess,
  Badge,
} from "../components";
import { useAuth } from "../hooks/useAuth";
import { usePermissions } from "../hooks/usePermissions";
import { Building2 } from "lucide-react";

export const AppLayout: React.FC = () => {
  const location = useLocation();
  const { userProfile } = useAuth();
  const { isSystemAdmin } = usePermissions();

  // Helper function to determine if a link is active
  const isActiveLink = (path: string) => {
    return (
      location.pathname === path || location.pathname.startsWith(path + "/")
    );
  };

  // Enhanced link styles with Salient design system
  const baseLinkStyles =
    "relative font-medium transition-all duration-200 px-3 py-2 rounded-lg text-sm";
  const activeLinkStyles =
    "bg-primary-50 text-primary-700 shadow-sm ring-1 ring-primary-200";
  const inactiveLinkStyles =
    "text-gray-600 hover:text-primary-600 hover:bg-primary-50/50";

  const getLinkStyles = (path: string) => {
    return `${baseLinkStyles} ${isActiveLink(path) ? activeLinkStyles : inactiveLinkStyles}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-gray-100 sticky top-0 z-50">
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link
              to="/app"
              className="text-xl font-bold text-primary-600 hover:text-primary-700 transition-colors duration-200 flex items-center space-x-2"
            >
              <Building2 className="h-6 w-6" />
              <span>Tech Notes</span>
            </Link>
            <div className="flex items-center space-x-1">
              {/* Always visible navigation items */}
              <Link to="/app" className={getLinkStyles("/app")}>
                Dashboard
              </Link>
              <Link to="/app/health" className={getLinkStyles("/app/health")}>
                Health
              </Link>
              <Link
                to="/app/showcase"
                className={getLinkStyles("/app/showcase")}
              >
                Showcase
              </Link>
              <Link to="/app/testing" className={getLinkStyles("/app/testing")}>
                Testing
              </Link>
              <Link to="/app/documents" className={getLinkStyles("/app/documents")}>
                Documents
              </Link>

              {/* Role-based navigation items */}
              <UserManagementAccess>
                <Link to="/app/users" className={getLinkStyles("/app/users")}>
                  Users
                </Link>
              </UserManagementAccess>

              <UserManagementAccess>
                <Link
                  to="/app/invitations"
                  className={getLinkStyles("/app/invitations")}
                >
                  Invitations
                </Link>
              </UserManagementAccess>

              <CompanyAdminOnly>
                <Link
                  to="/app/vehicles"
                  className={getLinkStyles("/app/vehicles")}
                >
                  Vehicles (Legacy)
                </Link>
              </CompanyAdminOnly>

              <CompanyAdminOnly>
                <Link
                  to="/app/vehicles-v2"
                  className={getLinkStyles("/app/vehicles-v2")}
                >
                  Vehicles V2
                </Link>
              </CompanyAdminOnly>

              <CompanyAdminOnly>
                <Link
                  to="/app/engagement"
                  className={getLinkStyles("/app/engagement")}
                >
                  Engagement
                </Link>
              </CompanyAdminOnly>

              <TenantManagementAccess>
                <Link
                  to="/app/settings"
                  className={getLinkStyles("/app/settings")}
                >
                  Settings
                </Link>
              </TenantManagementAccess>

              <SystemAdminOnly>
                <Link to="/app/admin" className={getLinkStyles("/app/admin")}>
                  Admin
                </Link>
              </SystemAdminOnly>

              {/* Tenant Info & User menu */}
              <div className="ml-4 pl-4 border-l border-gray-200/60 flex items-center space-x-3">
                {/* Tenant Information */}
                {userProfile?.tenantId && (
                  <div className="flex items-center space-x-2 text-sm bg-gray-50/80 px-3 py-1.5 rounded-lg border border-gray-200/60">
                    <Building2 className="h-4 w-4 text-primary-500" />
                    <div className="flex flex-col">
                      <span className="text-xs font-medium text-gray-600">
                        Tenant
                      </span>
                      <code className="text-xs bg-white px-2 py-0.5 rounded border font-mono text-gray-700">
                        {userProfile.tenantId.slice(-8)}
                      </code>
                    </div>
                    {isSystemAdmin && (
                      <Badge variant="admin" className="text-xs ml-2">
                        Admin
                      </Badge>
                    )}
                  </div>
                )}

                {/* User Button with enhanced styling */}
                <div className="p-1">
                  <UserButton
                    afterSignOutUrl="/"
                    appearance={{
                      elements: {
                        avatarBox:
                          "ring-2 ring-primary-100 hover:ring-primary-200 transition-all duration-200",
                        userButtonPopoverCard:
                          "shadow-xl border-0 ring-1 ring-gray-200",
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </nav>
      </header>

      <main className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white/60 backdrop-blur-sm rounded-xl shadow-sm border border-gray-200/60 min-h-[calc(100vh-12rem)]">
            <div className="p-6 sm:p-8">
              <Outlet />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};
