/**
 * API Contract Integration Tests
 * 
 * These tests validate that frontend API clients correctly handle
 * real backend API response structures, preventing contract mismatches.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TypedApiClient } from '../api-client';
import { 
  createApiResponse, 
  testApiContract, 
  isCreateDocumentResponse 
} from '@tech-notes/shared/test-utils/contract-testing';
import type { CreateDocumentResponse } from '@tech-notes/shared';

// Mock fetch to simulate backend responses
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('API Contract Tests', () => {
  let apiClient: TypedApiClient;
  const mockGetToken = vi.fn().mockResolvedValue('mock-token');

  beforeEach(() => {
    apiClient = new TypedApiClient(mockGetToken);
    mockFetch.mockClear();
  });

  describe('Documents API', () => {
    it('should handle createUploadUrl response correctly', async () => {
      // Mock backend response with proper ApiResponse structure
      const mockBackendResponse = createApiResponse<CreateDocumentResponse>({
        id: 'test-doc-id',
        tenantId: 'test-tenant-id',
        fileName: 'test.pdf',
        originalName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        s3Key: 'tenant-test/doc-test.pdf',
        createdBy: 'user-id',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        uploadUrl: 'https://s3.amazonaws.com/presigned-url',
        expiresIn: 900,
      }, {
        tenantId: 'test-tenant-id',
        expiresIn: 900,
      });

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockBackendResponse),
      });

      // Test the API contract
      const response = await testApiContract(
        () => apiClient.documents.createUploadUrl({
          fileName: 'test.pdf',
          fileSize: 1024,
          mimeType: 'application/pdf',
        }),
        isCreateDocumentResponse,
        'createUploadUrl'
      );

      // Verify the response structure
      expect(response.data.uploadUrl).toBeDefined();
      expect(typeof response.data.uploadUrl).toBe('string');
      expect(response.data.expiresIn).toBeDefined();
      expect(typeof response.data.expiresIn).toBe('number');
    });

    it('should fail gracefully when backend returns malformed response', async () => {
      // Mock malformed response (missing data wrapper)
      const malformedResponse = {
        uploadUrl: 'https://s3.amazonaws.com/presigned-url',
        expiresIn: 900,
        // Missing 'data' wrapper - this would cause the original bug
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(malformedResponse),
      });

      // This should throw an error due to contract validation
      await expect(
        testApiContract(
          () => apiClient.documents.createUploadUrl({
            fileName: 'test.pdf',
            fileSize: 1024,
            mimeType: 'application/pdf',
          }),
          isCreateDocumentResponse,
          'createUploadUrl'
        )
      ).rejects.toThrow('API response missing required "data" property');
    });
  });

  describe('Response Structure Validation', () => {
    it('should validate all API responses have consistent structure', async () => {
      const testCases = [
        {
          name: 'createUploadUrl',
          mockResponse: createApiResponse({
            id: 'test',
            uploadUrl: 'https://example.com',
            expiresIn: 900,
            fileName: 'test.pdf',
            fileSize: 1024,
            mimeType: 'application/pdf',
            s3Key: 'test-key',
            originalName: 'test.pdf',
            tenantId: 'tenant-id',
            createdBy: 'user-id',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          }),
          apiCall: () => apiClient.documents.createUploadUrl({
            fileName: 'test.pdf',
            fileSize: 1024,
            mimeType: 'application/pdf',
          }),
        },
      ];

      for (const testCase of testCases) {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(testCase.mockResponse),
        });

        const response = await testCase.apiCall();
        
        // All responses should have data property
        expect(response).toHaveProperty('data');
        expect(typeof response.data).toBe('object');
        
        // Meta is optional but should be object if present
        if ('meta' in response) {
          expect(typeof response.meta).toBe('object');
        }
      }
    });
  });
});
